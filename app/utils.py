import os
import re
import uuid
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from difflib import SequenceMatcher
from werkzeug.utils import secure_filename
from flask import current_app

# 配置日志
logger = logging.getLogger(__name__)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def save_uploaded_file(file, user_id):
    """保存上传的文件并返回文件路径"""
    if file and allowed_file(file.filename):
        # 生成安全的文件名
        filename = secure_filename(file.filename)
        # 添加UUID前缀避免文件名冲突
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # 创建用户专属目录
        user_dir = Path(current_app.config['UPLOAD_FOLDER']) / str(user_id)
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        file_path = user_dir / unique_filename
        file.save(str(file_path))
        
        return str(file_path)
    return None

def split_text_into_chapters(text_content):
    """将文本内容分割成章节"""
    chapters = []

    # 更全面的章节标题模式
    chapter_patterns = [
        r'第[一二三四五六七八九十百千万\d]+章[^\n]*',  # 第X章 + 可能的章节标题
        r'第[0-9]+章[^\n]*',  # 第数字章 + 可能的章节标题
        r'Chapter\s*\d+[^\n]*',  # Chapter数字 + 可能的标题
        r'[第]?[0-9]+[章节][^\n]*',  # 数字章/节 + 可能的标题
        r'[第]?[一二三四五六七八九十百千万]+[章节][^\n]*',  # 中文数字章/节 + 可能的标题
    ]

    # 尝试每个模式
    for pattern in chapter_patterns:
        # 查找所有章节标题
        chapter_titles = re.findall(pattern, text_content, flags=re.IGNORECASE | re.MULTILINE)

        if len(chapter_titles) > 1:  # 找到了多个章节
            # 按章节标题分割文本
            parts = re.split(f'({pattern})', text_content, flags=re.IGNORECASE | re.MULTILINE)

            current_title = None
            current_content = []
            chapter_num = 1

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # 检查是否是章节标题
                if re.match(pattern, part, flags=re.IGNORECASE | re.MULTILINE):
                    # 保存前一章
                    if current_title and current_content:
                        content_text = '\n\n'.join(current_content).strip()
                        if content_text:  # 只有内容不为空才添加
                            chapters.append({
                                'number': chapter_num,
                                'title': current_title,
                                'content': content_text
                            })
                            chapter_num += 1

                    # 开始新章
                    current_title = part
                    current_content = []
                else:
                    # 这是章节内容
                    if part.strip():
                        current_content.append(part)

            # 添加最后一章
            if current_title and current_content:
                content_text = '\n\n'.join(current_content).strip()
                if content_text:
                    chapters.append({
                        'number': chapter_num,
                        'title': current_title,
                        'content': content_text
                    })

            # 如果找到了章节，就返回结果
            if chapters:
                break

    # 如果没有找到章节标题，按内容长度智能分割
    if not chapters:
        # 按段落分割
        paragraphs = [p.strip() for p in re.split(r'\n\s*\n', text_content) if p.strip()]

        if len(paragraphs) <= 1:
            # 内容太少，整体作为一章
            chapters.append({
                'number': 1,
                'title': '第1章',
                'content': text_content.strip()
            })
        elif len(paragraphs) <= 10:
            # 段落较少，每个段落作为一章
            for i, para in enumerate(paragraphs, 1):
                chapters.append({
                    'number': i,
                    'title': f'第{i}章',
                    'content': para
                })
        else:
            # 段落较多，按字数智能分组
            total_chars = len(text_content)
            target_chapter_length = max(1000, total_chars // 20)  # 目标每章1000字或总长度的1/20

            current_chapter = []
            current_length = 0
            chapter_num = 1

            for para in paragraphs:
                para_length = len(para)

                # 如果加上这个段落会超过目标长度，且当前章节不为空，则开始新章
                if current_length + para_length > target_chapter_length and current_chapter:
                    chapter_content = '\n\n'.join(current_chapter)
                    chapters.append({
                        'number': chapter_num,
                        'title': f'第{chapter_num}章',
                        'content': chapter_content
                    })
                    chapter_num += 1
                    current_chapter = [para]
                    current_length = para_length
                else:
                    current_chapter.append(para)
                    current_length += para_length

            # 添加最后一章
            if current_chapter:
                chapter_content = '\n\n'.join(current_chapter)
                chapters.append({
                    'number': chapter_num,
                    'title': f'第{chapter_num}章',
                    'content': chapter_content
                })

    return chapters

def create_chapter_files(chapters, base_path):
    """为每个章节创建单独的文件"""
    chapter_files = []
    base_dir = Path(base_path).parent / f"{Path(base_path).stem}_chapters"
    base_dir.mkdir(parents=True, exist_ok=True)
    
    for chapter in chapters:
        chapter_filename = f"chapter_{chapter['number']:03d}.txt"
        chapter_path = base_dir / chapter_filename
        
        with open(chapter_path, 'w', encoding='utf-8') as f:
            f.write(chapter['content'])
        
        chapter_files.append(str(chapter_path))
    
    return str(base_dir), chapter_files

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = os.path.getsize(file_path)
        return round(size_bytes / (1024 * 1024), 2)
    except:
        return 0


# ==================== 文本处理工具函数 ====================

class BlockError(Exception):
    """自定义异常类，用于处理被阻止的请求"""
    pass


def is_allowed_character(char: str) -> bool:
    """检查字符是否为允许的字符类型（数字、英文、中文）"""
    if not char or char.isspace():
        return True

    # 检查数字
    if char.isdigit():
        return True

    # 检查英文字母
    if 'a' <= char.lower() <= 'z':
        return True

    # 检查中文字符
    if '\u4e00' <= char <= '\u9fff':
        return True

    # 标点符号等常见字符
    common_symbols = {'。', ' ', "'", '♀', ')', '€', '№', '∞', '℉', '&', '￥', '"', '=', '`', '《', '°', '.', '}', '¤', '|', '®', '「', '；', '*', '–', ':', '】', '{', '<', '¢', '\x0b', '…', '％', '℃', '~', '‱', '?', '》', '\r', ''', '[', '，', '」', '＝', '"', '©', '(', '、', '：', '【', '—', '（', '）', '\t', '"', '$', '/', '♂', '？', '‰', '-', '\\', '™', '£', '～', '_', ';', '\n', ']', '§', '！', '>', '!', '+', '%', '@', ''', ',', '#', '·', '\x0c', '¥'}
    if char in common_symbols:
        return True

    return False


def calculate_file_non_allowed_ratio(text: str) -> tuple:
    """计算整个文件中非法字符的占比"""
    non_space_chars = [char for char in text if not char.isspace()]
    if not non_space_chars:
        return 0.0, set(), 0, 0

    total_chars = len(non_space_chars)
    non_allowed_chars = set()
    non_allowed_count = 0

    for char in text:
        if not char.isspace() and not is_allowed_character(char):
            non_allowed_chars.add(char)
            non_allowed_count += 1

    ratio = non_allowed_count / total_chars if total_chars > 0 else 0
    return ratio, non_allowed_chars, non_allowed_count, total_chars


def retry_operation(operation: Callable, max_retries: int = 3, delay: float = 1.0, *args, **kwargs) -> Optional[Dict[str, Any]]:
    """通用重试函数"""
    for retry in range(max_retries):
        try:
            result = operation(*args, **kwargs)

            # 检查是否有有效结果
            if result and result.get("rewritten_text"):
                rewritten_text = result.get("rewritten_text")

                # 移除括号内容
                rewritten_text = re.sub(r"（.*?）", "", rewritten_text)
                rewritten_text = re.sub(r"\(.*?\)", "", rewritten_text)

                ratio, non_allowed_chars, non_allowed_count, total_chars = calculate_file_non_allowed_ratio(rewritten_text)
                threshold = 0.02  # 2% 阈值，调整为更宽松的设置

                # 计算英文字母占比
                english_chars = sum(1 for char in rewritten_text if 'a' <= char.lower() <= 'z')
                english_ratio = english_chars / total_chars if total_chars > 0 else 0
                english_threshold = 0.10  # 10% 阈值，调整为更宽松的设置

                # 检查质量 - 调整字符数阈值，使其更宽松
                min_chars = max(200, len(args[0]) * 0.3) if args else 50  # 动态设置最小字符数

                # 添加调试信息
                if non_allowed_chars:
                    logger.warning(f"检测到非法字符: {', '.join(list(non_allowed_chars)[:10])}{'...' if len(non_allowed_chars) > 10 else ''}")
                    logger.warning(f"非法字符数量: {non_allowed_count}, 总字符数: {total_chars}, 占比: {ratio:.2%}")

                if total_chars < min_chars:
                    logger.warning(f"总字符数{total_chars}小于最小要求{min_chars}，需要重试。")
                elif ratio >= threshold:
                    logger.warning(f"非法字符占比为 {ratio:.2%}，超过阈值 {threshold:.2%}，需要重试。")
                elif english_ratio >= english_threshold:
                    logger.warning(f"英文字母占比为 {english_ratio:.2%}，超过阈值 {english_threshold:.2%}，需要重试。")
                else:
                    logger.info(f"文本质量检查通过：字符数{total_chars}，非法字符占比{ratio:.2%}，英文占比{english_ratio:.2%}")
                    return result

        except BlockError as e:
            logger.warning(f"BlockError in {operation.__name__}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error during {operation.__name__}: {str(e)}")

        if retry < max_retries - 1:
            logger.info(f"Retrying {operation.__name__}, attempt {retry + 2}/{max_retries}")
            time.sleep(delay)
        else:
            # 最后一次重试失败，检查是否有部分可用的结果
            if result and result.get("rewritten_text"):
                logger.warning(f"最后一次重试，接受质量较低的结果：{len(result.get('rewritten_text', ''))}字符")
                return result

    logger.error(f"所有重试都失败了，{operation.__name__}返回None")
    return None


def extract_smart_ending(text):
    """智能提取文本结尾部分作为上下文"""
    if not text:
        return '无前文'

    # 按段落分割
    paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

    if not paragraphs:
        # 如果没有段落分割，直接取最后800字符
        return text[-800:] if len(text) > 800 else text

    # 策略：取最后2-3个段落，但不超过1000字符
    result_paragraphs = []
    total_length = 0
    max_length = 1000
    max_paragraphs = 3

    for paragraph in reversed(paragraphs[-max_paragraphs:]):
        if total_length + len(paragraph) <= max_length:
            result_paragraphs.insert(0, paragraph)
            total_length += len(paragraph)
        else:
            # 如果当前段落太长，截取部分
            remaining = max_length - total_length
            if remaining > 100:  # 至少保留100字符
                truncated = paragraph[-remaining:]
                result_paragraphs.insert(0, f"...{truncated}")
            break

    return '\n\n'.join(result_paragraphs) if result_paragraphs else text[-800:]


# ==================== 任务处理工具函数 ====================

def update_task_progress(celery_task, db_task, current, total, status, min_progress, max_progress):
    """更新任务进度"""
    # 计算实际进度（在min_progress和max_progress之间）
    if total > 0:
        progress_ratio = current / total
        actual_progress = min_progress + (max_progress - min_progress) * progress_ratio
    else:
        actual_progress = min_progress

    # 更新数据库
    db_task.progress = int(actual_progress)
    db_task.processed_chapters = current

    # 提交数据库事务
    from app import db
    db.session.commit()

    # 更新Celery状态
    celery_task.update_state(
        state='PROGRESS',
        meta={
            'current': int(actual_progress),
            'total': 100,
            'status': status,
            'chapters_current': current,
            'chapters_total': total
        }
    )


def merge_adapted_files(output_dir, task):
    """合并故事优化后的文件"""
    # 按章节号排序文件
    output_files = []
    for file_path in Path(output_dir).glob('*_rewrite.txt'):
        # 提取章节号进行排序
        match = re.search(r'chapter_(\d+)', file_path.name)
        if match:
            chapter_num = int(match.group(1))
            output_files.append((chapter_num, file_path))

    # 按章节号排序
    output_files.sort(key=lambda x: x[0])

    if not output_files:
        raise Exception("没有找到优化后的文件")

    # 创建最终输出文件
    final_filename = f"{task.task_name}_adapted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    final_path = Path(output_dir).parent / final_filename

    with open(final_path, 'w', encoding='utf-8') as outfile:
        outfile.write(f"《{task.book_name}》优化版\n")
        outfile.write(f"主角：{task.character}\n")
        outfile.write(f"频道：{task.channel}\n")
        outfile.write(f"人称：第{task.person}人称\n")
        outfile.write(f"优化时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        outfile.write("=" * 50 + "\n\n")

        for chapter_num, file_path in output_files:
            with open(file_path, 'r', encoding='utf-8') as infile:
                content = infile.read().strip()
                outfile.write(f"\n\n=== 第{chapter_num}章 ===\n\n")
                outfile.write(content)
                outfile.write("\n\n")

    return str(final_path)


# ==================== 文本相似度分析工具函数 ====================

def find_similar_chunks(text2: str, text1: str, chunk_size: int = 200, overlap: int = 50,
                       threshold: float = 0.3, min_pairs: int = 5, max_pairs: int = 10) -> List:
    """Find similar chunks between two texts using SequenceMatcher."""
    try:
        # 先移除特定字符，再移除空白字符
        texts = []
        for text in [text2, text1]:
            text = re.sub(r'[「」""''\u3000]', '', text)
            text = re.sub(r'\s', '', text)
            texts.append(text)

        def create_chunks(text: str) -> List[str]:
            sentences = re.split(r'([。！？])', text)
            sentences = [''.join(i) for i in zip(sentences[0::2], sentences[1::2] + [''])]
            sentences = [s for s in sentences if s.strip()]

            chunks = []
            current_chunk = []
            current_length = 0

            for sentence in sentences:
                sentence_length = len(sentence)

                if current_length + sentence_length > chunk_size and current_chunk:
                    chunks.append(''.join(current_chunk))
                    current_chunk = [current_chunk[-1]] if current_chunk else []
                    current_length = len(current_chunk[0]) if current_chunk else 0

                current_chunk.append(sentence)
                current_length += sentence_length

            if current_chunk:
                chunks.append(''.join(current_chunk))

            return chunks

        chunks_pairs = [create_chunks(text) for text in texts]

        if not chunks_pairs[0] or not chunks_pairs[1]:
            return []

        # 存储所有相似度超过阈值的配对
        all_pairs = []

        for chunk1 in chunks_pairs[0]:
            chunk_pairs = []
            for chunk2 in chunks_pairs[1]:
                similarity = SequenceMatcher(None, chunk1, chunk2).ratio()
                if similarity > threshold:
                    chunk_pairs.append((chunk1, chunk2, similarity))

            # 如果当前chunk1有匹配项，选择相似度最高的一个
            if chunk_pairs:
                best_pair = max(chunk_pairs, key=lambda x: x[2])
                all_pairs.append(best_pair)

        # 按相似度降序排序
        all_pairs.sort(key=lambda x: x[2], reverse=True)

        # 如果匹配数量少于min_pairs且有门槛值限制，逐步降低阈值直到满足最小数量
        if len(all_pairs) < min_pairs and threshold > 0.1:
            return find_similar_chunks(
                text2, text1, chunk_size, overlap,
                max(threshold - 0.1, 0.1),  # 不要低于0.1
                min_pairs, max_pairs
            )

        # 返回前max_pairs个结果，但如果结果数量小于min_pairs则返回所有结果
        return all_pairs[:max_pairs] if len(all_pairs) >= min_pairs else all_pairs

    except Exception as e:
        logging.error(f"Error in finding similar chunks: {str(e)}")
        return []


# ==================== 文本过滤和清理工具函数 ====================

def filter_think_tags(content: str) -> str:
    """过滤掉<think></think>标签及其内容"""
    if not content:
        return content
    return re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)


# ==================== 智能上下文处理工具函数 ====================

def get_smart_previous_context(output_path: Path, current_chapter_num: int) -> str:
    """智能获取前文上下文 - 优化并发处理版本"""
    if current_chapter_num <= 1:
        return '无前文'

    # 直接使用原始章节文件，支持真正的并发处理
    prev_chapter_num = current_chapter_num - 1
    original_prev_name = f"chapter_{prev_chapter_num:03d}.txt"
    original_prev_path = output_path.parent / f"{output_path.parent.name.replace('_adapted', '_chapters')}" / original_prev_name

    if original_prev_path.exists():
        try:
            prev_text = original_prev_path.read_text(encoding='utf-8')
            context = extract_smart_ending(prev_text)
            logger.info(f"使用第{prev_chapter_num}章原始文本作为上下文 (长度: {len(context)}字)")
            return context
        except Exception as e:
            logger.error(f"Error reading original chapter {prev_chapter_num}: {str(e)}")

    # 备用策略: 尝试获取更前面的原始章节
    for i in range(2, min(4, current_chapter_num)):  # 最多往前找2章
        alt_chapter_num = current_chapter_num - i
        alt_original_name = f"chapter_{alt_chapter_num:03d}.txt"
        alt_original_path = output_path.parent / f"{output_path.parent.name.replace('_adapted', '_chapters')}" / alt_original_name

        if alt_original_path.exists():
            try:
                alt_text = alt_original_path.read_text(encoding='utf-8')
                context = extract_smart_ending(alt_text)
                logger.info(f"使用第{alt_chapter_num}章原始文本作为替代上下文 (长度: {len(context)}字)")
                return f"[前文摘要] {context}"
            except Exception as e:
                continue

    return '无前文'


def wait_for_previous_chapter(prev_chapter_num: int, output_path: Path, completed_chapters: set, timeout: int = 300) -> bool:
    """等待前一章完成处理"""
    # 如果前一章已经完成，直接返回
    if prev_chapter_num in completed_chapters:
        return True

    # 检查前一章的输出文件是否存在
    prev_output_name = f"chapter_{prev_chapter_num:03d}_rewrite.txt"
    prev_output_path = output_path / prev_output_name

    if prev_output_path.exists():
        completed_chapters.add(prev_chapter_num)
        return True

    # 等待前一章完成，最多等待timeout秒
    start_time = time.time()
    while time.time() - start_time < timeout:
        if prev_chapter_num in completed_chapters or prev_output_path.exists():
            completed_chapters.add(prev_chapter_num)
            return True
        time.sleep(1)  # 每秒检查一次

    logger.warning(f"等待第{prev_chapter_num}章完成超时，继续处理")
    return False


# ==================== 文件处理和章节管理工具函数 ====================

def get_batch_previous_context(output_path: Path, first_chapter_num: int) -> str:
    """获取批次处理的前文上下文"""
    pre_text = '无前文'
    if first_chapter_num > 1:
        # 查找上一个批次的输出文件
        previous_batch_files = sorted(
            [f for f in output_path.glob('*_rewrite.txt')],
            key=lambda x: int(re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name).group(1)) if re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name) else 0
        )

        if previous_batch_files:
            # 取最后一个处理过的批次文件的后500字符作为pre_text
            last_batch_file = previous_batch_files[-1]
            try:
                pre_text = last_batch_file.read_text(encoding='utf-8')[-500:]
                logger.info(f"Retrieved pre_text from batch: {last_batch_file.name}")
            except Exception as e:
                logger.error(f"Error reading batch file {last_batch_file.name}: {str(e)}")
                pre_text = '无前文'

    return pre_text


def combine_batch_chapters(batch_files: List[Path]) -> tuple[str, List[str]]:
    """合并批次中的所有章节内容"""
    combined_text = ""
    chapter_info = []

    for file_path in batch_files:
        try:
            chapter_text = file_path.read_text(encoding='utf-8')
            chapter_num = int(re.search(r'\d+', file_path.stem).group())
            combined_text += f"\n\n=== 第{chapter_num}章 ===\n\n{chapter_text}"
            chapter_info.append(f"第{chapter_num}章")
            logger.info(f"Added chapter {chapter_num} to batch (length: {len(chapter_text)} characters)")
        except Exception as e:
            logger.error(f"Error reading file {file_path.name}: {str(e)}")
            continue

    return combined_text, chapter_info


def filter_chapter_files(input_files: List[Path], start_chapter: Optional[int] = None, end_chapter: Optional[int] = None) -> List[Path]:
    """根据章节范围过滤文件"""
    if start_chapter is None and end_chapter is None:
        return input_files

    filtered_files = []
    for f in input_files:
        chapter_num = int(re.search(r'\d+', f.stem).group())
        if start_chapter is not None and chapter_num < start_chapter:
            continue
        if end_chapter is not None and chapter_num > end_chapter:
            continue
        filtered_files.append(f)

    return filtered_files


def get_chapter_files_sorted(input_path: Path, exclude_rewrite: bool = True) -> List[Path]:
    """获取排序后的章节文件列表"""
    if exclude_rewrite:
        files = [f for f in input_path.glob('*.txt') if 'rewrite' not in f.stem]
    else:
        files = list(input_path.glob('*.txt'))

    return sorted(files, key=lambda x: int(re.search(r'\d+', x.stem).group()))


def extract_chapter_number(file_path: Path) -> int:
    """从文件路径中提取章节号"""
    match = re.search(r'\d+', file_path.stem)
    if match:
        return int(match.group())
    raise ValueError(f"Cannot extract chapter number from {file_path.name}")


def check_processed_chapters(input_files: List[Path], output_path: Path) -> tuple[int, List[Path]]:
    """检查已处理的章节并返回待处理的文件列表"""
    already_processed = 0
    pending_files = []

    for file_path in input_files:
        chapter_num = extract_chapter_number(file_path)
        output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
        output_file_path = output_path / output_name

        if output_file_path.exists():
            logger.info(f"Skipping already processed chapter: {chapter_num}")
            already_processed += 1
        else:
            pending_files.append(file_path)

    return already_processed, pending_files
